"""
基础智能体类 - 所有智能体的基类
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from langchain.agents import AgentExecutor, create_react_agent
from langchain.tools import BaseTool
from langchain_openai import ChatOpenAI
from langchain.prompts import PromptTemplate
from langchain.memory import ConversationBufferMemory
import logging

logger = logging.getLogger(__name__)

class BaseAgent(ABC):
    """所有智能体的基类，实现感知-思考-行动循环"""
    
    def __init__(
        self,
        name: str,
        description: str,
        llm: Optional[ChatOpenAI] = None,
        tools: Optional[List[BaseTool]] = None,
        memory: Optional[ConversationBufferMemory] = None
    ):
        self.name = name
        self.description = description
        self.llm = llm or ChatOpenAI(
            model="gpt-4-turbo-preview",
            temperature=0.7,
            max_tokens=4000
        )
        self.tools = tools or []
        self.memory = memory or ConversationBufferMemory(
            memory_key="chat_history",
            return_messages=True
        )
        self.agent_executor = None
        self._setup_agent()
    
    def _setup_agent(self):
        """设置智能体执行器"""
        if self.tools:
            prompt = self._get_prompt_template()
            agent = create_react_agent(
                llm=self.llm,
                tools=self.tools,
                prompt=prompt
            )
            self.agent_executor = AgentExecutor(
                agent=agent,
                tools=self.tools,
                memory=self.memory,
                verbose=True,
                handle_parsing_errors=True,
                max_iterations=10
            )
    
    @abstractmethod
    def _get_prompt_template(self) -> PromptTemplate:
        """获取智能体的提示模板"""
        pass
    
    @abstractmethod
    def _get_system_prompt(self) -> str:
        """获取系统提示"""
        pass
    
    def perceive(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """感知阶段 - 收集和处理输入信息"""
        logger.info(f"{self.name} 正在感知输入: {input_data}")
        return input_data
    
    def think(self, perceived_data: Dict[str, Any]) -> Dict[str, Any]:
        """思考阶段 - 分析信息并制定计划"""
        logger.info(f"{self.name} 正在思考...")
        return perceived_data
    
    def act(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """行动阶段 - 执行计划"""
        logger.info(f"{self.name} 正在执行行动...")
        if self.agent_executor:
            try:
                result = self.agent_executor.invoke(plan)
                return {"success": True, "result": result, "agent": self.name}
            except Exception as e:
                logger.error(f"{self.name} 执行失败: {str(e)}")
                return {"success": False, "error": str(e), "agent": self.name}
        else:
            return self._direct_action(plan)
    
    @abstractmethod
    def _direct_action(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """直接行动方法 - 当没有工具时的默认行动"""
        pass
    
    def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行完整的感知-思考-行动循环"""
        try:
            # 感知
            perceived_data = self.perceive(input_data)
            
            # 思考
            plan = self.think(perceived_data)
            
            # 行动
            result = self.act(plan)
            
            return result
        except Exception as e:
            logger.error(f"{self.name} 执行循环失败: {str(e)}")
            return {"success": False, "error": str(e), "agent": self.name}
    
    def get_status(self) -> Dict[str, Any]:
        """获取智能体状态"""
        return {
            "name": self.name,
            "description": self.description,
            "tools_count": len(self.tools),
            "has_executor": self.agent_executor is not None
        }
