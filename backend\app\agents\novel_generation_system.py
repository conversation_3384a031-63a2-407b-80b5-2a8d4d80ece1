"""
小说生成系统 - 多智能体协调器
"""
from typing import Dict, Any, List, Optional, TypedDict
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import ToolExecutor
import os
import json
import logging

from .orchestrator_agent import OrchestratorAgent
from .architect_agent import ArchitectAgent
from .chronicler_agent import ChroniclerAgent
from .lorekeeper_agent import LorekeeperAgent
from .editor_agent import EditorAgent

logger = logging.getLogger(__name__)

class NovelState(TypedDict):
    """小说生成状态"""
    user_prompt: str
    target_chapters: int
    current_chapter: int
    current_phase: str
    world_bible: Optional[Dict[str, Any]]
    characters: Optional[Dict[str, Any]]
    outline: Optional[Dict[str, Any]]
    chapter_content: str
    chapter_outline: str
    editor_feedback: str
    errors: List[str]
    completed_tasks: List[str]
    next_action: str
    iteration_count: int
    max_iterations: int

class NovelGenerationSystem:
    """小说生成系统 - 协调所有智能体的工作"""
    
    def __init__(self, openai_api_key: str = None, model: str = "gpt-4-turbo-preview"):
        """初始化系统"""
        # 设置API密钥
        if openai_api_key:
            os.environ["OPENAI_API_KEY"] = openai_api_key
        
        # 初始化LLM
        self.llm = ChatOpenAI(
            model=model,
            temperature=0.7,
            max_tokens=4000
        )
        
        # 初始化智能体
        self.lorekeeper = LorekeeperAgent(self.llm)
        self.orchestrator = OrchestratorAgent(self.llm)
        self.architect = ArchitectAgent(self.llm)
        self.chronicler = ChroniclerAgent(self.llm, self.lorekeeper)
        self.editor = EditorAgent(self.llm, self.lorekeeper)
        
        # 创建工作流图
        self.workflow = self._create_workflow()
        
        logger.info("小说生成系统初始化完成")
    
    def _create_workflow(self) -> StateGraph:
        """创建LangGraph工作流"""
        workflow = StateGraph(NovelState)
        
        # 添加节点
        workflow.add_node("orchestrator", self._orchestrator_node)
        workflow.add_node("architect", self._architect_node)
        workflow.add_node("chronicler", self._chronicler_node)
        workflow.add_node("editor", self._editor_node)
        workflow.add_node("lorekeeper", self._lorekeeper_node)
        
        # 设置入口点
        workflow.set_entry_point("orchestrator")
        
        # 添加条件边
        workflow.add_conditional_edges(
            "orchestrator",
            self._orchestrator_router,
            {
                "architect": "architect",
                "chronicler": "chronicler",
                "editor": "editor",
                "lorekeeper": "lorekeeper",
                "end": END
            }
        )
        
        workflow.add_conditional_edges(
            "architect",
            self._architect_router,
            {
                "orchestrator": "orchestrator",
                "lorekeeper": "lorekeeper",
                "end": END
            }
        )
        
        workflow.add_conditional_edges(
            "chronicler",
            self._chronicler_router,
            {
                "editor": "editor",
                "orchestrator": "orchestrator",
                "end": END
            }
        )
        
        workflow.add_conditional_edges(
            "editor",
            self._editor_router,
            {
                "chronicler": "chronicler",
                "lorekeeper": "lorekeeper",
                "orchestrator": "orchestrator",
                "end": END
            }
        )
        
        workflow.add_conditional_edges(
            "lorekeeper",
            self._lorekeeper_router,
            {
                "orchestrator": "orchestrator",
                "end": END
            }
        )
        
        return workflow.compile()
    
    def _orchestrator_node(self, state: NovelState) -> NovelState:
        """编排者节点"""
        try:
            logger.info(f"编排者处理阶段: {state['current_phase']}")
            
            if state["current_phase"] == "initialization":
                # 初始化项目
                result = self.orchestrator.execute({
                    "task_type": "initialize_project",
                    "user_prompt": state["user_prompt"],
                    "target_chapters": state["target_chapters"]
                })
                
                if result["success"]:
                    state["current_phase"] = "world_building"
                    state["next_action"] = "architect"
                    state["completed_tasks"].append("project_initialized")
                else:
                    state["errors"].append(result.get("error", "初始化失败"))
                    state["next_action"] = "end"
            
            elif state["current_phase"] == "world_building":
                state["next_action"] = "architect"
            
            elif state["current_phase"] == "generation":
                if state["current_chapter"] < state["target_chapters"]:
                    state["next_action"] = "chronicler"
                else:
                    state["current_phase"] = "completion"
                    state["next_action"] = "end"
            
            elif state["current_phase"] == "review":
                # 根据编辑反馈决定下一步
                if state["editor_feedback"] and "需要修改" in state["editor_feedback"]:
                    state["next_action"] = "chronicler"
                else:
                    state["current_chapter"] += 1
                    state["current_phase"] = "generation"
                    state["next_action"] = "lorekeeper"
            
            else:
                state["next_action"] = "end"
            
            return state
            
        except Exception as e:
            logger.error(f"编排者节点错误: {str(e)}")
            state["errors"].append(str(e))
            state["next_action"] = "end"
            return state
    
    def _architect_node(self, state: NovelState) -> NovelState:
        """架构师节点"""
        try:
            logger.info("架构师开始工作")
            
            if not state["world_bible"]:
                # 创建世界观
                result = self.architect.execute({
                    "task_type": "create_world_bible",
                    "user_prompt": state["user_prompt"]
                })
                
                if result["success"]:
                    state["world_bible"] = result["data"]
                    state["completed_tasks"].append("world_bible_created")
                else:
                    state["errors"].append(result.get("error", "世界观创建失败"))
            
            if not state["characters"]:
                # 创建角色
                result = self.architect.execute({
                    "task_type": "create_character_profiles",
                    "user_prompt": state["user_prompt"]
                })
                
                if result["success"]:
                    state["characters"] = result["data"]
                    state["completed_tasks"].append("characters_created")
                else:
                    state["errors"].append(result.get("error", "角色创建失败"))
            
            if not state["outline"]:
                # 创建大纲
                result = self.architect.execute({
                    "task_type": "create_plot_outline",
                    "user_prompt": state["user_prompt"],
                    "target_chapters": state["target_chapters"]
                })
                
                if result["success"]:
                    state["outline"] = result["data"]
                    state["completed_tasks"].append("outline_created")
                    state["current_phase"] = "generation"
                    state["current_chapter"] = 1
                else:
                    state["errors"].append(result.get("error", "大纲创建失败"))
            
            state["next_action"] = "lorekeeper"
            return state
            
        except Exception as e:
            logger.error(f"架构师节点错误: {str(e)}")
            state["errors"].append(str(e))
            state["next_action"] = "orchestrator"
            return state
    
    def _chronicler_node(self, state: NovelState) -> NovelState:
        """执笔者节点"""
        try:
            logger.info(f"执笔者开始撰写第{state['current_chapter']}章")
            
            # 获取章节大纲
            chapter_outline = self._get_chapter_outline(state["outline"], state["current_chapter"])
            state["chapter_outline"] = chapter_outline
            
            # 撰写章节
            result = self.chronicler.execute({
                "task_type": "write_chapter",
                "chapter_number": state["current_chapter"],
                "chapter_outline": chapter_outline
            })
            
            if result["success"]:
                state["chapter_content"] = result["content"]
                state["completed_tasks"].append(f"chapter_{state['current_chapter']}_written")
                state["next_action"] = "editor"
            else:
                state["errors"].append(result.get("error", f"第{state['current_chapter']}章撰写失败"))
                state["next_action"] = "orchestrator"
            
            return state
            
        except Exception as e:
            logger.error(f"执笔者节点错误: {str(e)}")
            state["errors"].append(str(e))
            state["next_action"] = "orchestrator"
            return state
    
    def _editor_node(self, state: NovelState) -> NovelState:
        """校订者节点"""
        try:
            logger.info(f"校订者开始审查第{state['current_chapter']}章")
            
            # 审查章节
            result = self.editor.execute({
                "task_type": "review_chapter",
                "chapter_number": state["current_chapter"],
                "chapter_content": state["chapter_content"],
                "chapter_outline": state["chapter_outline"]
            })
            
            if result["success"]:
                state["editor_feedback"] = result["feedback"]
                
                if result["passed"]:
                    state["current_phase"] = "review"
                    state["next_action"] = "lorekeeper"
                    state["completed_tasks"].append(f"chapter_{state['current_chapter']}_approved")
                else:
                    # 需要重写
                    state["next_action"] = "chronicler"
                    logger.info(f"第{state['current_chapter']}章需要修改")
            else:
                state["errors"].append(result.get("error", f"第{state['current_chapter']}章审查失败"))
                state["next_action"] = "orchestrator"
            
            return state
            
        except Exception as e:
            logger.error(f"校订者节点错误: {str(e)}")
            state["errors"].append(str(e))
            state["next_action"] = "orchestrator"
            return state
    
    def _lorekeeper_node(self, state: NovelState) -> NovelState:
        """典籍官节点"""
        try:
            logger.info("典籍官更新知识库")
            
            # 索引设定文档
            if state["world_bible"] and "world_bible_indexed" not in state["completed_tasks"]:
                self.lorekeeper.execute({
                    "task_type": "index_content",
                    "content_type": "settings",
                    "content": json.dumps(state["world_bible"], ensure_ascii=False),
                    "metadata": {"type": "world_bible"}
                })
                state["completed_tasks"].append("world_bible_indexed")
            
            if state["characters"] and "characters_indexed" not in state["completed_tasks"]:
                self.lorekeeper.execute({
                    "task_type": "index_content",
                    "content_type": "settings",
                    "content": json.dumps(state["characters"], ensure_ascii=False),
                    "metadata": {"type": "characters"}
                })
                state["completed_tasks"].append("characters_indexed")
            
            # 索引章节内容
            if state["chapter_content"]:
                self.lorekeeper.execute({
                    "task_type": "update_memory",
                    "chapter_number": state["current_chapter"],
                    "chapter_content": state["chapter_content"]
                })
                state["completed_tasks"].append(f"chapter_{state['current_chapter']}_indexed")
            
            state["next_action"] = "orchestrator"
            return state
            
        except Exception as e:
            logger.error(f"典籍官节点错误: {str(e)}")
            state["errors"].append(str(e))
            state["next_action"] = "orchestrator"
            return state
    
    def _get_chapter_outline(self, outline: Dict[str, Any], chapter_number: int) -> str:
        """获取章节大纲"""
        try:
            if not outline:
                return f"第{chapter_number}章大纲"
            
            chapter_outlines = outline.get("chapter_outlines", [])
            if chapter_number <= len(chapter_outlines):
                return chapter_outlines[chapter_number - 1]
            else:
                return f"第{chapter_number}章：继续主线情节发展"
                
        except Exception as e:
            logger.warning(f"获取章节大纲失败: {str(e)}")
            return f"第{chapter_number}章大纲"
    
    # 路由函数
    def _orchestrator_router(self, state: NovelState) -> str:
        return state.get("next_action", "end")
    
    def _architect_router(self, state: NovelState) -> str:
        return state.get("next_action", "orchestrator")
    
    def _chronicler_router(self, state: NovelState) -> str:
        return state.get("next_action", "editor")
    
    def _editor_router(self, state: NovelState) -> str:
        return state.get("next_action", "orchestrator")
    
    def _lorekeeper_router(self, state: NovelState) -> str:
        return state.get("next_action", "orchestrator")
    
    def generate_novel(
        self,
        user_prompt: str,
        target_chapters: int = 100,
        max_iterations: int = 1000
    ) -> Dict[str, Any]:
        """生成小说"""
        try:
            logger.info(f"开始生成小说，目标章节数：{target_chapters}")
            
            # 创建数据目录
            os.makedirs("data", exist_ok=True)
            os.makedirs("data/chapters", exist_ok=True)
            
            # 初始化状态
            initial_state = NovelState(
                user_prompt=user_prompt,
                target_chapters=target_chapters,
                current_chapter=0,
                current_phase="initialization",
                world_bible=None,
                characters=None,
                outline=None,
                chapter_content="",
                chapter_outline="",
                editor_feedback="",
                errors=[],
                completed_tasks=[],
                next_action="orchestrator",
                iteration_count=0,
                max_iterations=max_iterations
            )
            
            # 执行工作流
            final_state = self.workflow.invoke(initial_state)
            
            # 生成结果报告
            result = {
                "success": len(final_state["errors"]) == 0,
                "completed_chapters": final_state["current_chapter"],
                "target_chapters": target_chapters,
                "errors": final_state["errors"],
                "completed_tasks": final_state["completed_tasks"],
                "world_bible": final_state["world_bible"],
                "characters": final_state["characters"],
                "outline": final_state["outline"]
            }
            
            logger.info(f"小说生成完成，共完成{final_state['current_chapter']}章")
            return result
            
        except Exception as e:
            logger.error(f"小说生成失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "completed_chapters": 0,
                "target_chapters": target_chapters
            }
