"""
智能体系统配置
"""
from pydantic_settings import BaseSettings
from typing import Dict, Any, List
import os

class AgentConfig(BaseSettings):
    """智能体系统配置"""
    
    # API配置
    openai_api_key: str = ""
    openai_model: str = "gpt-4-turbo-preview"
    openai_temperature: float = 0.7
    openai_max_tokens: int = 4000
    
    # 系统配置
    max_iterations: int = 1000
    max_concurrent_tasks: int = 5
    
    # 文件路径配置
    data_dir: str = "data"
    chapters_dir: str = "data/chapters"
    summaries_dir: str = "data/summaries"
    vectorstore_dir: str = "data/vectorstore"
    
    # RAG配置
    chunk_size: int = 1000
    chunk_overlap: int = 200
    top_k_results: int = 5
    
    # "爽文"创作配置
    shuang_point_interval: int = 7  # 每7章一个爽点
    golden_chapters: int = 3  # 黄金三章
    min_chapter_words: int = 2000
    max_chapter_words: int = 4000
    
    class Config:
        env_file = ".env"
        env_prefix = "AGENT_"

# 爽文创作规则配置
SHUANG_WEN_RULES = {
    "golden_three_chapters": {
        "chapter_1": {
            "requirements": [
                "主角在受压迫/不利状态下登场",
                "展示主角的困境和不甘",
                "暗示主角的潜力或特殊之处"
            ],
            "avoid": [
                "过度描写环境",
                "冗长的背景介绍",
                "缓慢的节奏"
            ]
        },
        "chapter_2": {
            "requirements": [
                "迅速引入核心冲突和反派",
                "主角面临直接威胁或挑战",
                "为'金手指'的出现做铺垫"
            ],
            "avoid": [
                "拖沓的情节发展",
                "过多的支线剧情"
            ]
        },
        "chapter_3": {
            "requirements": [
                "揭示或首次使用'金手指'",
                "完成第一个次要'爽点'",
                "建立主角的成长方向"
            ],
            "avoid": [
                "过于轻易的胜利",
                "缺乏后续发展的铺垫"
            ]
        }
    },
    
    "shuang_point_types": [
        "打脸装逼",
        "能力突破",
        "复仇雪恨",
        "获得宝物",
        "收服强者",
        "震惊众人",
        "逆转局势",
        "惩恶扬善"
    ],
    
    "character_archetypes": {
        "antagonists": [
            "傲慢少爷",
            "嫉妒同辈",
            "势利长辈",
            "腐败官员",
            "邪恶强者",
            "背叛朋友"
        ],
        "supporting": [
            "忠诚伙伴",
            "智慧导师",
            "红颜知己",
            "可靠盟友"
        ]
    },
    
    "power_system_rules": [
        "等级分明，便于比较强弱",
        "突破有条件，不能随意",
        "金手指有规则，不能无敌",
        "实力提升要有代价",
        "战斗要有策略性"
    ]
}

# 提示模板配置
PROMPT_TEMPLATES = {
    "world_building": """
你是一位经验丰富的{genre}小说世界观设计师。请基于以下用户需求创建详细的世界观设定：

用户需求：{user_prompt}

请创建包含以下要素的完整世界观：

1. 世界基础设定
   - 世界名称和背景历史
   - 时代特征和社会结构
   - 地理环境和重要地点
   - 文化传统和价值观念

2. 力量体系设计
   - 修炼/能力体系名称
   - 详细的等级划分（至少7个等级）
   - 每个等级的特征和能力
   - 突破条件和修炼方法
   - 体系的限制和代价

3. "金手指"设定
   - 名称和基本概念
   - 具体功能和使用方式
   - 成长规律和进化路径
   - 使用限制和代价
   - 与世界观的融合度

4. 势力分布
   - 主要门派/组织（至少5个）
   - 势力间的关系和冲突
   - 地盘分布和影响力
   - 重要人物和领袖

5. 重要地点
   - 关键场景描述
   - 地点的特殊性质
   - 相关传说或历史
   - 对情节的重要性

请确保设定符合"{genre}"类型的特点，并为后续的"爽文"情节发展提供充足的素材。
""",
    
    "character_creation": """
你是一位专业的{genre}小说角色设计师。请基于世界观设定创建丰富的角色体系：

世界观设定：
{world_bible}

请创建以下角色：

1. 主角设定
   - 基本信息（姓名、年龄、外貌、出身）
   - 性格特点和行为模式
   - 背景故事和成长经历
   - 初始实力和天赋
   - 与"金手指"的关系
   - 成长目标和动机
   - 性格缺陷和成长空间

2. 主要配角（3-5个）
   - 基本信息和背景
   - 与主角的关系类型
   - 在故事中的作用
   - 个人成长弧线
   - 独特的能力或特长

3. 主要反派（3-5个）
   - 基本信息和背景
   - 反派类型（从以下选择：{antagonist_types}）
   - 与主角的冲突根源
   - 实力水平和手段
   - 被打脸的方式设计
   - 最终结局安排

4. 重要NPC
   - 师父/导师角色
   - 家族/宗门成员
   - 势力领袖
   - 其他关键人物

每个角色都要有清晰的动机、鲜明的特点和明确的故事作用。特别注意反派要符合"爽文"的打脸套路。
""",
    
    "plot_outline": """
你是一位{genre}"爽文"情节规划专家。请基于世界观和角色设定创建详细的情节大纲：

世界观设定：
{world_bible}

角色设定：
{characters}

目标章节数：{target_chapters}

请创建包含以下内容的分层大纲：

1. 总体情节弧线
   - 起始状态：主角的具体困境
   - 发展阶段：能力提升的关键节点
   - 冲突升级：敌人实力的递进
   - 高潮阶段：最终对决的设计
   - 结局状态：逆袭成功的具体表现

2. 分卷规划
   - 将{target_chapters}章分为若干卷（每卷20-30章）
   - 每卷的主题和核心冲突
   - 每卷的主要"爽点"安排
   - 主角实力提升的节点
   - 重要角色的登场时机

3. 黄金三章详细设计
   - 第一章：{golden_chapter_1_requirements}
   - 第二章：{golden_chapter_2_requirements}
   - 第三章：{golden_chapter_3_requirements}

4. "爽点"分布规划
   - 每{shuang_interval}章安排一个主要"爽点"
   - "爽点"类型：{shuang_types}
   - 打脸情节的具体设计
   - 能力突破的时机安排
   - 复仇节点的规划

5. 详细章节概要
   - 前20章的详细概要（每章300-500字）
   - 后续章节的概括性规划
   - 重要情节点的标注
   - 角色关系的发展轨迹

请确保情节紧凑，符合"爽文"的节奏要求，每个章节都有明确的目标和推进作用。
"""
}

def get_agent_config() -> AgentConfig:
    """获取智能体配置"""
    return AgentConfig()

def get_shuang_wen_rules() -> Dict[str, Any]:
    """获取爽文创作规则"""
    return SHUANG_WEN_RULES

def get_prompt_template(template_name: str) -> str:
    """获取提示模板"""
    return PROMPT_TEMPLATES.get(template_name, "")
