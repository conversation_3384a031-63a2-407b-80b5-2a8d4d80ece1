import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:novel_app/services/ai_agent_novel_service.dart';
import 'package:novel_app/controllers/api_config_controller.dart';
import 'package:novel_app/models/model_config.dart';
import 'dart:async';

class AIAgentNovelController extends GetxController {
  // UI控制器
  final TextEditingController promptController = TextEditingController();
  
  // 响应式变量
  final RxBool isGenerating = false.obs;
  final RxDouble progress = 0.0.obs;
  final RxString currentStatus = '准备开始...'.obs;
  final RxInt targetChapters = 10.obs;
  final RxString selectedGenre = '修仙'.obs;
  final Rxn<Map<String, dynamic>> generationResult = Rxn<Map<String, dynamic>>();

  // 使用现有的API配置控制器
  late final ApiConfigController _apiConfigController;
  
  // 智能体状态
  final RxMap<String, String> agentStatuses = <String, String>{
    '编排者': 'waiting',
    '架构师': 'waiting', 
    '执笔者': 'waiting',
    '典籍官': 'waiting',
    '校订者': 'waiting',
  }.obs;
  
  // 可选类型
  final List<String> genres = [
    '修仙',
    '都市异能',
    '玄幻',
    '科幻',
    '历史',
    '军事',
    '游戏',
    '二次元',
  ];
  
  // 服务实例
  late final AIAgentNovelService _service;
  
  // 生成任务相关
  String? _currentTaskId;
  Timer? _progressTimer;
  
  @override
  void onInit() {
    super.onInit();
    _service = Get.put(AIAgentNovelService());
    _apiConfigController = Get.find<ApiConfigController>();
  }

  /// 获取当前模型配置
  ModelConfig get currentModelConfig => _apiConfigController.currentConfig.value;

  /// 从配置中获取提供商类型
  String _getProviderFromConfig(ModelConfig config) {
    // 根据apiUrl判断提供商类型
    if (config.apiUrl.contains('openai.com')) {
      return 'openai';
    } else if (config.apiUrl.contains('deepseek.com')) {
      return 'deepseek';
    } else if (config.apiUrl.contains('moonshot.cn')) {
      return 'moonshot';
    } else if (config.apiUrl.contains('localhost:11434')) {
      return 'local_ollama';
    } else if (config.apiUrl.contains('bigmodel.cn')) {
      return 'zhipu';
    } else {
      return 'custom';
    }
  }
  
  @override
  void onClose() {
    promptController.dispose();
    _progressTimer?.cancel();
    super.onClose();
  }
  
  /// 开始生成小说
  Future<void> startGeneration() async {
    if (promptController.text.trim().isEmpty) {
      Get.snackbar('错误', '请输入创作提示');
      return;
    }
    
    try {
      isGenerating.value = true;
      progress.value = 0.0;
      currentStatus.value = '正在初始化AI智能体系统...';
      generationResult.value = null;
      
      // 重置智能体状态
      agentStatuses.updateAll((key, value) => 'waiting');
      
      // 使用当前模型配置调用后端API开始生成
      final config = currentModelConfig;
      final response = await _service.startGeneration(
        prompt: promptController.text.trim(),
        targetChapters: targetChapters.value,
        genre: selectedGenre.value,
        provider: _getProviderFromConfig(config),
        apiKey: config.apiKey,
        baseUrl: config.apiUrl,
        model: config.model,
        temperature: config.temperature,
        maxTokens: config.maxTokens,
      );
      
      if (response['success']) {
        _currentTaskId = response['task_id'];
        currentStatus.value = '生成任务已启动，正在协调智能体...';
        
        // 开始轮询进度
        _startProgressPolling();
      } else {
        throw Exception(response['message'] ?? '启动生成失败');
      }
    } catch (e) {
      isGenerating.value = false;
      Get.snackbar('错误', '启动生成失败: $e');
    }
  }
  
  /// 开始轮询进度
  void _startProgressPolling() {
    _progressTimer = Timer.periodic(const Duration(seconds: 2), (timer) async {
      if (_currentTaskId == null) {
        timer.cancel();
        return;
      }
      
      try {
        final status = await _service.getGenerationStatus(_currentTaskId!);
        
        if (status['success']) {
          _updateProgress(status);
          
          // 检查是否完成
          if (status['status'] == 'completed' || status['status'] == 'failed') {
            timer.cancel();
            await _handleGenerationComplete(status);
          }
        }
      } catch (e) {
        print('轮询进度失败: $e');
      }
    });
  }
  
  /// 更新进度信息
  void _updateProgress(Map<String, dynamic> status) {
    final progressData = status['progress'] ?? {};
    final currentChapter = progressData['current_chapter'] ?? 0;
    final totalChapters = progressData['target_chapters'] ?? targetChapters.value;
    final currentPhase = progressData['current_phase'] ?? 'initialization';
    
    // 更新进度条
    if (totalChapters > 0) {
      progress.value = currentChapter / totalChapters;
    }
    
    // 更新状态文本
    switch (currentPhase) {
      case 'initialization':
        currentStatus.value = '正在初始化项目...';
        agentStatuses['编排者'] = 'active';
        break;
      case 'world_building':
        currentStatus.value = '架构师正在构建世界观和角色...';
        agentStatuses['编排者'] = 'completed';
        agentStatuses['架构师'] = 'active';
        break;
      case 'generation':
        currentStatus.value = '执笔者正在撰写第${currentChapter}章...';
        agentStatuses['架构师'] = 'completed';
        agentStatuses['执笔者'] = 'active';
        agentStatuses['典籍官'] = 'active';
        break;
      case 'review':
        currentStatus.value = '校订者正在审查第${currentChapter}章...';
        agentStatuses['校订者'] = 'active';
        break;
      case 'completion':
        currentStatus.value = '正在完成最终整理...';
        agentStatuses.updateAll((key, value) => 'completed');
        break;
      default:
        currentStatus.value = '处理中...';
    }
  }
  
  /// 处理生成完成
  Future<void> _handleGenerationComplete(Map<String, dynamic> status) async {
    isGenerating.value = false;
    final taskId = _currentTaskId;
    _currentTaskId = null;

    if (status['status'] == 'completed') {
      // 获取完整结果
      try {
        final result = await _service.getGenerationResult(taskId!);
        generationResult.value = result;
        
        agentStatuses.updateAll((key, value) => 'completed');
        currentStatus.value = '生成完成！';
        
        Get.snackbar(
          '成功',
          '小说生成完成！共${result['completed_chapters']}章',
          backgroundColor: Colors.green.shade100,
          colorText: Colors.green.shade800,
        );
      } catch (e) {
        generationResult.value = {
          'success': false,
          'error': '获取结果失败: $e',
        };
      }
    } else {
      // 生成失败
      generationResult.value = {
        'success': false,
        'error': status['error'] ?? '生成失败',
      };
      
      Get.snackbar(
        '失败',
        '小说生成失败: ${status['error'] ?? '未知错误'}',
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }
  
  /// 取消生成
  Future<void> cancelGeneration() async {
    if (_currentTaskId == null) return;
    
    try {
      await _service.cancelGeneration(_currentTaskId!);
      
      _progressTimer?.cancel();
      isGenerating.value = false;
      _currentTaskId = null;
      
      agentStatuses.updateAll((key, value) => 'waiting');
      currentStatus.value = '已取消生成';
      
      Get.snackbar('提示', '已取消生成任务');
    } catch (e) {
      Get.snackbar('错误', '取消失败: $e');
    }
  }
  
  /// 重置生成
  void resetGeneration() {
    isGenerating.value = false;
    progress.value = 0.0;
    currentStatus.value = '准备开始...';
    generationResult.value = null;
    _currentTaskId = null;
    _progressTimer?.cancel();
    
    agentStatuses.updateAll((key, value) => 'waiting');
  }
  
  /// 下载小说
  Future<void> downloadNovel() async {
    if (_currentTaskId == null || generationResult.value == null) {
      Get.snackbar('错误', '没有可下载的小说');
      return;
    }
    
    try {
      Get.dialog(
        const Center(
          child: CircularProgressIndicator(),
        ),
        barrierDismissible: false,
      );
      
      final downloadResult = await _service.downloadNovel(_currentTaskId!);
      
      Get.back(); // 关闭加载对话框
      
      if (downloadResult['success']) {
        // 显示下载成功对话框
        Get.dialog(
          AlertDialog(
            title: const Text('下载成功'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('小说标题: ${downloadResult['title'] ?? '未知'}'),
                Text('总字数: ${downloadResult['word_count'] ?? 0}'),
                Text('章节数: ${downloadResult['chapters'] ?? 0}'),
                const SizedBox(height: 12),
                const Text('小说内容已保存到本地文件。'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Get.back(),
                child: const Text('确定'),
              ),
            ],
          ),
        );
      } else {
        throw Exception(downloadResult['error'] ?? '下载失败');
      }
    } catch (e) {
      Get.back(); // 确保关闭加载对话框
      Get.snackbar('错误', '下载失败: $e');
    }
  }
  
  /// 获取用户的生成任务列表
  Future<void> loadUserTasks() async {
    try {
      final tasks = await _service.getUserTasks();
      // 可以在这里处理任务列表，比如显示历史生成记录
      print('用户任务: $tasks');
    } catch (e) {
      print('获取用户任务失败: $e');
    }
  }
  
  /// 设置快速模板
  void setTemplate(String template) {
    promptController.text = template;
  }
}
