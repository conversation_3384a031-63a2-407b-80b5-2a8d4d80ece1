import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AIAgentSettingsScreen extends StatefulWidget {
  const AIAgentSettingsScreen({super.key});

  @override
  State<AIAgentSettingsScreen> createState() => _AIAgentSettingsScreenState();
}

class _AIAgentSettingsScreenState extends State<AIAgentSettingsScreen> {
  final TextEditingController _apiKeyController = TextEditingController();
  final TextEditingController _baseUrlController = TextEditingController();

  String _selectedProvider = 'openai';
  String _selectedModel = 'gpt-4-turbo-preview';
  bool _isLoading = false;
  bool _showApiKey = false;

  final Map<String, Map<String, dynamic>> _providers = {
    'openai': {
      'name': 'OpenAI',
      'models': ['gpt-4-turbo-preview', 'gpt-4', 'gpt-3.5-turbo', 'gpt-3.5-turbo-16k'],
      'defaultBaseUrl': 'https://api.openai.com/v1',
      'requiresApiKey': true,
    },
    'local_ollama': {
      'name': '本地 Ollama',
      'models': ['llama2', 'llama2:13b', 'mistral', 'qwen', 'chatglm3'],
      'defaultBaseUrl': 'http://localhost:11434/v1',
      'requiresApiKey': false,
    },
    'deepseek': {
      'name': 'DeepSeek',
      'models': ['deepseek-chat', 'deepseek-coder'],
      'defaultBaseUrl': 'https://api.deepseek.com/v1',
      'requiresApiKey': true,
    },
    'moonshot': {
      'name': '月之暗面 Kimi',
      'models': ['moonshot-v1-8k', 'moonshot-v1-32k', 'moonshot-v1-128k'],
      'defaultBaseUrl': 'https://api.moonshot.cn/v1',
      'requiresApiKey': true,
    },
    'custom': {
      'name': '自定义 API',
      'models': ['custom-model'],
      'defaultBaseUrl': 'http://localhost:8080/v1',
      'requiresApiKey': false,
    },
  };

  List<String> get _currentModels => _providers[_selectedProvider]?['models'] ?? [];

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  @override
  void dispose() {
    _apiKeyController.dispose();
    _baseUrlController.dispose();
    super.dispose();
  }

  Future<void> _loadSettings() async {
    setState(() => _isLoading = true);

    try {
      final prefs = await SharedPreferences.getInstance();
      _selectedProvider = prefs.getString('ai_agent_provider') ?? 'openai';
      _apiKeyController.text = prefs.getString('ai_agent_api_key') ?? '';
      _baseUrlController.text = prefs.getString('ai_agent_base_url') ??
          _providers[_selectedProvider]?['defaultBaseUrl'] ?? '';
      _selectedModel = prefs.getString('ai_agent_model') ??
          _currentModels.isNotEmpty ? _currentModels.first : 'gpt-4-turbo-preview';
    } catch (e) {
      Get.snackbar('错误', '加载设置失败: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _saveSettings() async {
    setState(() => _isLoading = true);

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('ai_agent_provider', _selectedProvider);
      await prefs.setString('ai_agent_api_key', _apiKeyController.text.trim());
      await prefs.setString('ai_agent_base_url', _baseUrlController.text.trim());
      await prefs.setString('ai_agent_model', _selectedModel);

      Get.snackbar(
        '成功',
        '设置已保存',
        backgroundColor: Colors.green.shade100,
        colorText: Colors.green.shade800,
      );
    } catch (e) {
      Get.snackbar('错误', '保存设置失败: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  String _getApiKeyHint() {
    switch (_selectedProvider) {
      case 'openai':
        return '您可以在 https://platform.openai.com/api-keys 获取API密钥';
      case 'deepseek':
        return '您可以在 https://platform.deepseek.com 获取API密钥';
      case 'moonshot':
        return '您可以在 https://platform.moonshot.cn 获取API密钥';
      default:
        return '请联系服务提供商获取API密钥';
    }
  }

  Future<void> _testConnection() async {
    final requiresApiKey = _providers[_selectedProvider]?['requiresApiKey'] == true;
    if (requiresApiKey && _apiKeyController.text.trim().isEmpty) {
      Get.snackbar('提示', '请先输入API密钥');
      return;
    }

    setState(() => _isLoading = true);
    
    try {
      // 这里可以添加实际的连接测试逻辑
      await Future.delayed(const Duration(seconds: 2)); // 模拟测试
      
      Get.snackbar(
        '成功',
        'API连接测试成功',
        backgroundColor: Colors.green.shade100,
        colorText: Colors.green.shade800,
      );
    } catch (e) {
      Get.snackbar('错误', '连接测试失败: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('AI智能体设置'),
        backgroundColor: Colors.purple.shade100,
        actions: [
          IconButton(
            onPressed: _isLoading ? null : _saveSettings,
            icon: const Icon(Icons.save),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // API配置
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.key, color: Colors.blue.shade600),
                              const SizedBox(width: 8),
                              Text(
                                'API配置',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.blue.shade600,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),

                          // 提供商选择
                          const Text(
                            '模型提供商',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 8),
                          DropdownButtonFormField<String>(
                            value: _selectedProvider,
                            decoration: const InputDecoration(
                              border: OutlineInputBorder(),
                            ),
                            items: _providers.entries.map((entry) {
                              return DropdownMenuItem(
                                value: entry.key,
                                child: Text(entry.value['name']),
                              );
                            }).toList(),
                            onChanged: (value) {
                              if (value != null) {
                                setState(() {
                                  _selectedProvider = value;
                                  _baseUrlController.text = _providers[value]?['defaultBaseUrl'] ?? '';
                                  final models = _currentModels;
                                  if (models.isNotEmpty) {
                                    _selectedModel = models.first;
                                  }
                                });
                              }
                            },
                          ),
                          const SizedBox(height: 16),

                          // API密钥（如果需要）
                          if (_providers[_selectedProvider]?['requiresApiKey'] == true) ...[
                            const Text(
                              'API密钥',
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                          const SizedBox(height: 8),
                          TextField(
                            controller: _apiKeyController,
                            obscureText: !_showApiKey,
                            decoration: InputDecoration(
                              hintText: '请输入您的OpenAI API密钥',
                              border: const OutlineInputBorder(),
                              suffixIcon: IconButton(
                                onPressed: () {
                                  setState(() => _showApiKey = !_showApiKey);
                                },
                                icon: Icon(
                                  _showApiKey ? Icons.visibility_off : Icons.visibility,
                                ),
                              ),
                            ),
                          ),
                            const SizedBox(height: 8),
                            Text(
                              _getApiKeyHint(),
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey.shade600,
                              ),
                            ),
                            const SizedBox(height: 16),
                          ],
                          
                          // 服务器地址
                          const Text(
                            '服务器地址',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 8),
                          TextField(
                            controller: _baseUrlController,
                            decoration: const InputDecoration(
                              hintText: 'http://localhost:8000',
                              border: OutlineInputBorder(),
                            ),
                          ),
                          const SizedBox(height: 16),
                          
                          // 模型选择
                          const Text(
                            'AI模型',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 8),
                          DropdownButtonFormField<String>(
                            value: _selectedModel,
                            decoration: const InputDecoration(
                              border: OutlineInputBorder(),
                            ),
                            items: _currentModels.map((model) {
                              return DropdownMenuItem(
                                value: model,
                                child: Text(model),
                              );
                            }).toList(),
                            onChanged: (value) {
                              if (value != null) {
                                setState(() => _selectedModel = value);
                              }
                            },
                          ),
                          const SizedBox(height: 16),
                          
                          // 测试连接按钮
                          SizedBox(
                            width: double.infinity,
                            child: OutlinedButton.icon(
                              onPressed: _testConnection,
                              icon: const Icon(Icons.wifi_tethering),
                              label: const Text('测试连接'),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // 使用说明
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.info_outline, color: Colors.orange.shade600),
                              const SizedBox(width: 8),
                              Text(
                                '使用说明',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.orange.shade600,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          const Text(
                            '1. 获取OpenAI API密钥：\n'
                            '   访问 https://platform.openai.com/api-keys\n'
                            '   注册账号并创建API密钥\n\n'
                            '2. 配置服务器：\n'
                            '   确保后端服务正在运行\n'
                            '   默认地址为 http://localhost:8000\n\n'
                            '3. 选择模型：\n'
                            '   推荐使用 gpt-4-turbo-preview 获得最佳效果\n'
                            '   gpt-3.5-turbo 成本更低但质量稍差\n\n'
                            '4. 测试连接：\n'
                            '   保存设置后点击"测试连接"验证配置',
                            style: TextStyle(fontSize: 14, height: 1.4),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // 成本提醒
                  Card(
                    color: Colors.yellow.shade50,
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.warning_amber, color: Colors.orange.shade600),
                              const SizedBox(width: 8),
                              Text(
                                '成本提醒',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.orange.shade600,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          const Text(
                            '• 使用OpenAI API会产生费用，请注意控制使用量\n'
                            '• 生成长篇小说（100章+）可能消耗较多token\n'
                            '• 建议先用少量章节测试效果\n'
                            '• 可以在OpenAI控制台查看使用情况和费用',
                            style: TextStyle(fontSize: 14, height: 1.4),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),

                  // 保存按钮
                  SizedBox(
                    width: double.infinity,
                    height: 50,
                    child: ElevatedButton.icon(
                      onPressed: _saveSettings,
                      icon: const Icon(Icons.save),
                      label: const Text('保存设置'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.purple.shade600,
                        foregroundColor: Colors.white,
                        textStyle: const TextStyle(fontSize: 16),
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }
}
